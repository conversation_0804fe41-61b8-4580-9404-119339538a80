
// var a = id('com.sdu.didi.psnger:id/popClose').findOne().parent().click()   //关闭弹窗

// var a = id('com.sdu.didi.psnger:id/bt_refresh').findOne().click()  //点击刷新按钮

// swipe(device.width / 2, device.height * 3 / 4, device.width / 2, device.height / 4, 500);  //向下滑动5毫米


// var a = id('com.sdu.didi.psnger:id/sfc_order_card_tips_content').findOne().parent().parent().parent().parent().click();
// log(a.parent().parent().parent().parent().click())    点击匹配的订单

// var a = id('com.sdu.didi.psnger:id/btn_main_title').findOne();
// log(a.parent().parent())   //点击邀请同行
// waitForActivity('com.sdu.didi.psnger:id/btn_main_title');


// var sum = id('com.sdu.didi.psnger:id/sfc_order_price_content').find(); //获取订单金额

// var distance = id('com.sdu.didi.psnger:id/sfc_order_card_tips_content').find(); //获取距离

// distance.forEach((child, index) => {
//     var num = child.text()
//     var Passenger = parseInt(num.match(/\d+/)[0]);//使用正则表达式获取 乘客人数     num.match(/\d+/)[0]; 
//     var CarPooling = num.match(/[\u4e00-\u9fff]+/);   //使用正则表达式获取 “独享” “愿拼”
//     //numb? (JSON.stringify((numb[0].slice(1)).toString())) : '';      将变量转换为string类型
//     var result =(CarPooling && CarPooling[0].slice(1))? (CarPooling[0].slice(1)).toString() : '';//numb? (numb[0].slice(1)).toString() : '';//使用正则表达式获取 “独享” “愿拼”
//     var distance = num.match(/\d+\.\d+/)[0]; 

//     // log(numa)   //输出 乘客人数
//     log(distance)  //输出 愿拼或者独享

//     if (Passenger === 1 && result==="愿拼" && distance >= parseFloat(0)) {      //参数由前端传入当同时满足两个条件时执行
//         sum.forEach((child,index) =>{
//             var num = child.text()
//             var Passenger = parseFloat(num.match(/\d+(\.\d+)?/)[0]); //使用正则表达式获取订单金额
//             log("金额:"+Passenger+result)
//         });
        
//         log("符合")   //符合逻辑开始抢单
//     }else{
//         log("不符合")
//     }
//     // sleep(random(parseInt(myApp.delayMin), parseInt(myApp.delayMax)) * 1000) //随机延迟
// });