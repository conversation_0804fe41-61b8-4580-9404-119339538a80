// "ui";

var myApp = {};
myApp.title = "问天一号" //脚本名称
myApp.packageName = "com.sdu.didi.psnger" //程序包名
myApp.appVersion = "6.8.4" //app版本
myApp.characteristic="wentian"  //存储标识

myApp.mainMenu = 0  //下拉菜单索引
myApp.delayMin = "1" //随机延迟最小值 秒
myApp.delayMax = "3" //随机延迟最大值 秒
myApp.OrderAmount = 15 //订单最低金额   大于用户设置的金额开始接单
myApp.LocalOrder = "1" //  1：市内订单  2:跨城市订单  3:常用路线订单
myApp.CarrsMill = 32 //订单最低总距离   大于用户设置的距离开始接单
myApp.pax = "愿拼" // 愿拼车用户设置
myApp.RiderShip = 1   //乘客人数
myApp.Glide = 1   //向下滑动开关 1为下滑  0为不下滑

App_滴滴获取订单()

// ui.layout(
//     <ScrollView>
//     <vertical>
//         <appbar>
//             <toolbar id="toolbar" title="问天一号_内部测试版 Test:V0.2"/>
//         </appbar>
//         {/* 判断无障碍服务 悬浮窗权限是否开启 */}
//         <card w="*" h="auto" margin="10 5" cardCornerRadius="2dp" cardElevation="1dp" gravity="center_vertical">
//             <vertical padding="18 8" h="auto">
//                 <linear>
//                     <Switch id="autoService" text="无障碍服务:" checked="{{auto.service != null}}" w="auto" textStyle="bold" />
//                     <Switch id="悬浮窗权限" text="悬浮窗权限" checked="{{floaty.checkPermission() != false}}" padding="8 8 8 8" textSize="15sp"/>
//                 </linear>
//             </vertical>
//             <View bg="#E51400" h="*" w="5" />
//         </card>

//         <card w="*" h="70" margin="10 5" cardCornerRadius="2dp"
//             cardElevation="1dp" gravity="center_vertical">       
//         <vertical padding="18 8" h="auto">
//             <linear>
//                 <horizontal>
//                     <text textSize="16sp">功能选择</text>
//                     <spinner id="mainMenu" entries="滴滴顺风车抢单|哈啰顺风车抢单" />
//                 </horizontal>
//             </linear>
//         </vertical>
//         <View bg="#f44336" h="*" w="10" />
//         </card>

//         <card w="*" h="70" margin="10 5" cardCornerRadius="2dp"
//             cardElevation="1dp" gravity="center_vertical">       
//         <vertical padding="18 8" h="auto">
//             <linear>
//                 <text text="订单金额:" textColor="black" w="auto" />
//                 <input id="OrderAmount" color="black" inputType="number" w="60" />
//                 <text text="元，抢单-" textColor="black" w="auto" />
//                 <text text="注:默认值为：0" textColor="red" w="auto" />
//             </linear>
//         </vertical>
//         <View bg="#f44336" h="*" w="10" />
//         </card>
//         <button style="Widget.AppCompat.Button.Colored" id="start" margin="10">启动</button>
//     </vertical>
//     </ScrollView>
// );

// getDate(true);  //读取界面配置


// // 无障碍服务开关单击事件
// ui.autoService.on("check",function(checked){
//     // 用户勾选无障碍服务的选项时，跳转到页面让用户去开启
//     if(checked && auto.service == null){
//         app.startActivity({
//             action:"android.settings.ACCESSIBILITY_SETTINGS"
//         });
//     }
//     if(!checked && auto.service != null){
//         auto.service.disableSelf();
//     }
// });

// ui.悬浮窗权限.on("check", function(checked) {
//     //申请悬浮窗
//     importClass(android.content.Intent);
//     importClass(android.net.Uri);
//     importClass(android.provider.Settings);
//     var intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
//         Uri.parse("package:" + context.getPackageName()));
//     intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//     app.startActivity(intent);
// });

// ui.emitter.on("resume",function(){
//     //此时根据无障碍服务的开启情况，同步开关的状态
//     ui.autoService.check = auto.service != null;
//     ui.悬浮窗权限.checked = floaty.checkPermission() != false
// });

// //启动按钮单击事件
// ui.start.on("click",()=>{
//     saveDate();  //保存界面配置
//     getDate(false);  //读取界面配置

//     //程序开始运行之前判断无障碍服务
//     if(auto.service == null){
//         toastLog("请先开启无障碍服务!");
//         return;
//     }

//     if (floaty.checkPermission() == false) {
//         toast("请先开启悬浮窗权限！")
//         return;
//     }

//     // //屏蔽音量键调节声音 按音量上键停止脚本
//     // events.setKeyInterceptionEnabled("volume_up",true);
//     // //启用按键监听
//     // events.observeKey();
//     // //监听音量键按下
//     // events.onKeyDown("volume_up", () =>{
//     //     toastLog('按音量键停止');
//     //     exit();
//     // });

//     threads.start(function(){
//         //在新线程执行的代码
//         main();
//     });

// });

// function main(){
//     if(myApp.mainMenu == "0"){
//         app.launch(myApp.packageName)  //启动滴滴
//         waitForActivity("com.didi.sdk.app.MainActivity")  //等待进入滴滴主界面
//         App_滴滴获取订单();
//     }else if(myApp.mainMenu == "1"){
//         log("哈啰顺风车开发中")
//     }
// }

// App_滴滴获取订单();

// App_滴滴获取订单();

// /* 遍历订单列表 公里数 */
// var distance = id('com.sdu.didi.psnger:id/from_tv_tag').find(); //获取距离

// distance.forEach((child, index) => {
//     var num = child.text()
//     var numa = parseFloat(num.split('km')[0]);
//     log(numa)
//     if(numa < myApp.CarrsMill){
//         toastLog("满足条件")
//         return false;
//     }else{
//         toastLog("不满足条件")
//     }
// });






//封装函数
function App_滴滴获取订单() { //已完成
    while (true) {
        if (id("com.sdu.didi.psnger:id/sfc_home_end_address_tv").text("你要去哪儿").exists()) {
            //toastLog("当前在接单界面")
            swipe(device.width / 2, device.height * 3 / 4, device.width / 2, device.height / 4, 500); //下滑500毫米
            //执行判断是否在车主界面   待开发

            /* 遍历订单列表  金额 */
            var sum = id('com.sdu.didi.psnger:id/sfc_order_price_content').find(); //获取订单金额
            var distance1 = id('com.sdu.didi.psnger:id/sfc_order_card_tips_content').find(); //获取订单 乘客人数 愿拼 订单总路程 
            
            sum.forEach((child, index) => {
                var num = child.text()
                var numa = parseFloat(num.match(/\d+(\.\d+)?/)[0]); //使用正则表达式获取订单金额

                sleep(random(parseInt(myApp.delayMin), parseInt(myApp.delayMax)) * 1000) //随机延迟

                if (numa > myApp.OrderAmount || myApp.OrderAmount == 0) { // 判断numa的金额是否满足条件  numa由前端用户传入 默认值为:0

                    /* 用户设置金额满足条件后开始判断距离 */
                    distance1.forEach((child,index) =>{
                        var num = child.text()
                        var Passenger = parseFloat(num.match(/\d+(\.\d+)?/)[0]); //使用正则表达式获取乘客人数
                        var CarPooling = num.match(/[\u4e00-\u9fff]+/);   //使用正则表达式获取 “独享” “愿拼”
                        var result =(CarPooling && CarPooling[0].slice(1))? (CarPooling[0].slice(1)).toString() : '';//numb? (numb[0].slice(1)).toString() : '';//使用正则表达式获取 “独享” “愿拼”
                        var distance = num.match(/\d+\.\d+/)[0]; 
                        // log("金额符合---"+"乘客人数:"+Passenger+"拼车"+result+"距离："+distance);
                        log(result)
                        if(Passenger === myApp.RiderShip && result === myApp.pax && distance >= parseFloat(myApp.CarrsMill)){   
                            log("满足条件开始接单:---"+"乘客人数："+Passenger+"拼车："+result+"---订单总距离:"+distance)     /* 当前默认值myApp.pax："愿拼"    myApp.CarrsMill：0 */
                            id('com.sdu.didi.psnger:id/sfc_order_card_tips_content').findOne().parent().parent().parent().parent().click();  //点击匹配的订单
                            // id('com.sdu.didi.psnger:id/btn_main_title').findOne().parent().parent().click();  //点击邀请同行
                        }else if(id('com.sdu.didi.psnger:id/bt_refresh').exists() && myApp.Glide === 1){
                            swipe(device.width / 2, device.height * 3 / 4, device.width / 2, device.height / 4, 500);
                            log(myApp.RiderShip+"---"+result+"---"+distance+"---");
                            log(myApp.pax+"---"+myApp.CarrsMill);
                        };
                    });
                    /* ------------------------------------------------------------------------------------------- */
                } else{
                    swipe(device.width / 2, device.height * 3 / 4, device.width / 2, device.height / 4, 500); //向下滑动5毫米  刷新按钮控件ID：fullId('com.sdu.didi.psnger:id/bt_refresh')
                    log("金额不符合")

                    /* 下面的代码是下滑3次后如果 */
                //     let scrollCount = 0;
                //     while (scrollCount < 3){
                //          // 执行下滑操作
                //             swipe(device.width / 2, device.height * 3 / 4, device.width / 2, device.height / 4, 500);
                //             scrollCount++;
                //             // 下滑 3 次后点击刷新按钮
                //             id('com.sdu.didi.psnger:id/bt_refresh').findOne().click();
                //     };
                };
            });
            //错误处理
            if (id("com.sdu.didi.psnger:id/popClose").exists()) {
                log("在弹窗界面 已关闭弹窗")
                id('com.sdu.didi.psnger:id/popClose').findOne().parent().click()  //关闭弹窗
            };
        }else{
            id("com.sdu.didi.psnger:id/normal_icon").findOne().parent().parent().click();  //点击车主按钮
        };
    };
};


// //保存界面配置
// function saveDate(){
//     //ui.mainMenu.getSelectedltemPosition()   获取下拉菜单索引
//     setStorageData(myApp.characteristic,"mainMenu",ui.mainMenu.getSelectedItemPosition())

//     //获取输入框的值
//     setStorageData(myApp.characteristic,"OrderAmount",ui.OrderAmount.text())

// };

// //读取界面配置
// //isSetVlue:是否设置ui组件的值   逻辑型参数
// function getDate(isSetVlue){
//     // 判断本地是否存储菜单的索引值。
//     if(getStorageData(myApp.characteristic,"mainMenu") != undefined){
//         // 重新定义菜单索引
//         myApp.mainMenu = getStorageData(myApp.characteristic,"mainMenu")
//         // 设置菜单选中项目
//         isSetVlue && ui.mainMenu.setSelection(myApp.mainMenu);

//         // if(isSetVlue){
//         //     ui.mainMenu.setSelection(myApp.mainMenu);
//         // }
//     };

//     if(getStorageData(myApp.characteristic,"OrderAmount") != undefined){
//         myApp.OrderAmount = getStorageData(myApp.characteristic,"OrderAmount")
//     };
//     isSetVlue && ui.OrderAmount.setText(myApp.OrderAmount);
// };




// //保存本地数据
// function setStorageData(name, key,value){
//     const storage=storages.create(name); //创建storage对象
//     storage.put(key, value);
// };

// //读取本地数据
// function getStorageData(name, key){
//     const storage=storages.create(name);//创建storage对象
//     if(storage.contains(key)){
//         return storage.get(key, "")
//     };
//     //默认返回undefined
// };

// //删除本地数据
// function delstorageData(name, key){
//     const storage =storages.create(name); //创建storage对象
//     if(storage.contains(key)){
//         storage.remove(key);
//     };
// };


toast('问天一号_内部测试版 Test:V0.2 切勿外传')