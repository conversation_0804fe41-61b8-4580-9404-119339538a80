"ui";
ui.statusBarColor("#003300");

function Sdk() {
  this.debug = false; // 调试模式 true = 开  false = 关 【注意：打包前务必关闭调试模式】
  this.mode = 0; // 计费模式 该值为 0 时为计时模式，该值为 1 时为计次模式
  this.appId = "66430ef6"; // 项目ID 开发后台对应项目的项目ID
  this.secretKey = "vjb1jd8g4jUKak60"; // 加密密钥 开发后台对应项目的加密密钥
  this.version = 1; // 本地版本 当后台设置的项目版本大于本地版本时，提示用户更新并跳转到下载网址
  this.updateMode = 0; // 更新模式 该值为 0 时不强制更新，该值为 1 时强制更新（首选）
  this.changeMode = 1; // 换机模式 该值为 0 时为顶号换机，该值为 1 时为智能换机（首选）
  /** 【顶号换机】当卡号在第二台设备使用时，不用换机码就可以换机，同时前一台设备自动解绑下线
     【智能换机】强烈推荐 ! 当卡号在第二台设备使用时，需要输入换机码才能换机 ! 用户购买换机码，二次为你带来收益 ! */
  this.deviceId = this.getDevId(); // 设备ID可通过各种方式获取，使用你认为可靠的获取方式即可
  this.api = this.switch();
}



/**
 * 手动切换最佳线路 - 首选
 * UI设置一个 id 为 sp1 的下拉框，下拉框 3 个选项，对应 3 条不同线路
 */
Sdk.prototype.manualSwitch = function () {
  let sp, url;
  sp = ui.sp1.getSelectedItemPosition();
  switch (sp) {
    case 1:
      url = 'https://api.kushao.net/v3/';
      break;
    case 2:
      url = 'https://api.kushao.018888.xyz/v3/';
      break;
    default:
      url = 'https://api.kushao.netauth.asia/v3/';
      break;
  }
  this.api = url;
  this.okApi = url;
  this.ping = true;
};


/**
* 线路切换 相关方法，无需理会
*/
Sdk.prototype.switch = function () {
  let url, result;
  if (this.ping) return this.okApi;
  try {
    url = 'https://api.kushao.net/v3/';
    result = http.get(url);
    if (result.statusCode === 200) {
      this.ping = true;
      this.okApi = url;
      return url;
    } else {
      this.ping = false;
      throw '连接失败';
    }
  } catch (error) {
    console.log(error);
    url = 'https://api.kushao.018888.xyz/v3/';
    result = http.get(url);
    if (result.statusCode === 200) {
      this.ping = true;
      this.okApi = url;
      return url;
    }
  }
};



/**
 * HttpPost 相关方法，无需理会
 */
Sdk.prototype.httpPost = function (apiRoute, data, sign) {
  let edata = this.encrypt(data);
  try {
    let result = http.post(
      this.api + apiRoute,
      {
        data: edata,
        sign: sign,
      },
      {
        headers: {
          "User-Agent": "AutoJs_ID_" + this.appId,
        },
      }
    );

    if (result.statusCode === 200) {
      result = result.body.string();
      if (this.debug) {
        console.log("请求参数(加密前)：" + data);
        console.log("返回数据：" + result);
      }

      if (this.isJSON(result)) {
        result = JSON.parse(result);
        if (/^\d+$/.test(result.code)) {
          result = { code: this.encrypt(result.code), msg: this.encrypt(result.msg) };
        }

        return result;
      } else {
        console.log("返回数据：" + result);
        throw "未知错误";
      }
    } else {
      throw "请更换认证线路尝试";
    }
  } catch (error) {
    return { code: this.encrypt("403"), msg: this.encrypt(error) };
  }
};



/**
 * SHA1算法 相关方法，无需理会
 */
Sdk.prototype.sha1 = function (data) {
  return $crypto.digest(data, "SHA-1");
};



/**
 * AES加密 相关方法，无需理会
 */
Sdk.prototype.encrypt = function (data) {
  let key = new $crypto.Key(this.secretKey);
  return $crypto.encrypt(data, key, "AES/ECB/PKCS7Padding", {
    output: "base64",
  });
};



/**
 * AES解密 相关方法，无需理会
 */
Sdk.prototype.decrypt = function (data) {
  let key = new $crypto.Key(this.secretKey);
  return $crypto.decrypt(data, key, "AES/ECB/PKCS7Padding", {
    input: "base64",
    output: "string",
  });
};



/**
 * 网络时间 相关方法，无需理会
 */
Sdk.prototype.t = function () {
  try {
    let url = "https://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp";
    let result = http.get(url);
    result = result.body.json();
    return result.data.t.slice(0, 10);
  } catch (error) {
    let timeStamp = new Date().getTime();
    return String(timeStamp).slice(0, 10);
  }
};



/**
 * 转时间戳 相关方法，无需理会
 */
Sdk.prototype.toTime = function (str) {
  return Date.parse(str.replace(/-/g, "/")) / 1000;
};



/**
 * 当前时间 相关方法，无需理会
 */
Sdk.prototype.time = function () {
  let today = new Date();
  let h = today.getHours();
  let m = today.getMinutes();
  let s = today.getSeconds();
  if (m < 10) {
    m = "0" + m;
  }
  if (s < 10) {
    s = "0" + s;
  }
  return h + ":" + m + ":" + s;
};



/**
 * 取机器码 相关方法，无需理会
 */
Sdk.prototype.getDevId = function () {
  let str = this.sha1(device.serial + device.getAndroidId());
  return str.slice(10, 30);
};



/**
 * JSON判断 相关方法，无需理会
 */
Sdk.prototype.isJSON = function (str) {
  if (typeof str == "string") {
    try {
      let obj = JSON.parse(str);
      return !!(typeof obj == "object" && obj);
    } catch (e) {
      log(e);
      return false;
    }
  }
  log("It is not a string !");
};



/**
 * 发起QQ对话
 */
Sdk.prototype.qqMessage = function (qq) {
  let fmt = /^\d{5,11}$/;
  if (fmt.test(qq)) {
    if (getAppName("com.tencent.mobileqq") == null) {
      toast("Q Q 未 安 装 或 版 本 不 支 持");
    } else {
      app.startActivity({
        action: "android.intent.action.VIEW",
        data: "mqq://im/chat?chat_type=wpa&version=1&src_type=web&uin=" + qq,
        packageName: "com.tencent.mobileqq",
      });
    }
  } else {
    toast("联 系 信 息 未 配 置 或 格 式 错 误");
  }
};



/**
 * 自定义对话框
 */
Sdk.prototype.dialog = function (message, col, type) {
  let wait = threads.disposable();
  let color = "#D94600";
  let color1 = "#006030";
  if (col == 1) {
    color = "#006030";
    color1 = "#D94600";
  }
  if (type == 1) {
    type = "取 消";
  } else {
    type = "";
  }
  dialogs
    .build({
      contentColor: color,
      content: message,
      contentLineSpacing: 1.5,
      negativeColor: color1,
      positiveColor: color1,
      negative: type,
      positive: "确  定",
      type: "app-or-overlay",
    })
    .on("positive", () => {
      wait.setAndNotify(true);
    })
    .on("negative", () => {
      wait.setAndNotify(false);
    })
    .show();
  return wait.blockedGet();
};



/**
 * 得到项目配置信息
 * @param select 取值范围 0-12 不同取值返回不同结果，详见开发文档
 * @return  JSON  返回加密的JSON，详见开发文档
 */
Sdk.prototype.appInfo = function (select) {
  let t = this.t();
  let apiRoute = "Project/appInfo/" + this.appId;
  let sign = this.sha1(apiRoute + select + t + this.secretKey);
  let data = select + "|" + t;
  return this.httpPost(apiRoute, data, sign);
};



/**
 * 执行远程函数
 * @param funName 函数名称
 * @param params 函数的参数，多个参数用英文逗号分隔
 * @return string  成功返回函数执行结果，失败返回空字符
 */
Sdk.prototype.runFunction = function (funName, params) {
  let t = this.t();
  let apiRoute = "Variable/runFunction/" + this.appId;
  let sign = this.sha1(apiRoute + funName + params + this.token + t + this.secretKey);
  let data = funName + "|" + params + "|" + this.token + "|" + t;
  let res = this.httpPost(apiRoute, data, sign);
  let code = Number(this.decrypt(res.code));
  if (code === 200) {
    let t1 = this.decrypt(res.timeStamp);
    let result = this.decrypt(res.result);
    let sign1 = this.sha1(code + result + t1 + this.secretKey);
    if (res.sign !== sign1 || Math.abs(t - t1) > 60) {
      return '';
    }
    return result;
  } else {
    console.log(this.decrypt(res.msg));
    return '';
  }
};



/**
 * 读取远程变量
 * @param varName 变量名称
 * @return string 成功返回变量值，失败返回空字符
 */
Sdk.prototype.getValue = function (varName) {
  let t = this.t();
  let apiRoute = "Variable/getVal/" + this.appId;
  let sign = this.sha1(apiRoute + varName + this.token + t + this.secretKey);
  let data = varName + "|" + this.token + "|" + t;
  let res = this.httpPost(apiRoute, data, sign);
  let code = Number(this.decrypt(res.code));
  if (code === 200) {
    let t1 = this.decrypt(res.timeStamp);
    let value = this.decrypt(res.value);
    let sign1 = this.sha1(code + value + t1 + this.secretKey);
    if (res.sign !== sign1 || Math.abs(t - t1) > 60) {
      return '';
    }
    return value;
  } else {
    console.log(this.decrypt(res.msg));
    return '';
  }
};



/**
 * 添加远程变量
 * @param varName 变量名称
 * @param value 新变量值
 * @param remark 功能备注
 * @return boolean 成功返回 true，失败返回 false
 */
Sdk.prototype.addVal = function (varName, value, remark) {
  let t = this.t();
  let apiRoute = "Variable/addVal/" + this.appId;
  let sign = this.sha1(apiRoute + varName + value + remark + t + this.secretKey);
  let data = varName + "|" + value + "|" + remark + "|" + t;
  let res = this.httpPost(apiRoute, data, sign);
  let code = Number(this.decrypt(res.code));
  if (code === 200) {
    console.log("远 程 变 量 添 加 成 功");
    return true;
  } else {
    console.log(this.decrypt(res.msg));
    return false;
  }
};



/**
 * 远程变量赋值
 * @param varName 变量名称
 * @param value 新变量值
 * @return boolean 成功返回 true，失败返回 false
 */
Sdk.prototype.setValue = function (varName, value) {
  let t = this.t();
  let apiRoute = "Variable/setVal/" + this.appId;
  let sign = this.sha1(apiRoute + varName + value + t + this.secretKey);
  let data = varName + "|" + value + "|" + t;
  let res = this.httpPost(apiRoute, data, sign);
  let code = Number(this.decrypt(res.code));
  if (code === 200) {
    console.log("远 程 变 量 更 新 成 功");
    return true;
  } else {
    console.log(this.decrypt(res.msg));
    return false;
  }
};



/**
 * 更新卡密备注
 * @param authCode 卡号或账号
 * @param remark 新的卡号备注信息
 * @return boolean 成功返回 true，失败返回 false
 */
Sdk.prototype.setRemark = function (authCode, remark) {
  let t = this.t();
  let apiRoute = "Variable/setRemark/" + this.appId;
  let sign = this.sha1(apiRoute + authCode + remark + t + this.secretKey);
  let data = authCode + "|" + remark + "|" + t;
  let res = this.httpPost(apiRoute, data, sign);
  let code = Number(this.decrypt(res.code));
  if (code === 200) {
    console.log("卡 密 备 注 更 新 成 功");
    return true;
  } else {
    console.log(this.decrypt(res.msg));
    return false;
  }
};



/**
 * 网络验证综合接口，密码传入空值视为单卡模式，反之视为账号密码模式
 * @param 账号 账号或卡号
 * @param 密码 密码
 * @return JSON  返回加密的JSON，详见开发文档，认证失败直接停止所有线程
 */
Sdk.prototype.verify = function (账号, 密码) {
  this.success = false;
  let usr = String(账号).trim();
  let pwd = String(密码).trim();
  let fmt = /^[\w.-]{5,30}$/;
  if (!fmt.test(usr)) {
    this.dialog("输 入 长 度 或 格 式 不 符 合 规 则", 0, 0);
    threads.shutDownAll();
  }
  let t = this.t();
  let apiRoute = "License/verify/" + this.appId;
  let sign = this.sha1(apiRoute + this.mode + usr + pwd + this.deviceId + t + this.secretKey);
  let data = this.mode + "|" + usr + "|" + pwd + "|" + this.deviceId + "|" + t;
  let res = this.httpPost(apiRoute, data, sign);
  let code = Number(this.decrypt(res.code));
  if (code === 200) {
    let retSign, endDate, surplusCount;
    let t1 = Number(this.decrypt(res.timeStamp));
    this.token = this.decrypt(res.token);
    this.remark = this.decrypt(res.remark);

    if (this.mode === 0) {
      endDate = this.decrypt(res.endDate);
      retSign = this.sha1(code + endDate + this.token + this.remark + t1 + this.secretKey);
    } else {
      surplusCount = this.decrypt(res.surplusCount);
      retSign = this.sha1(code + surplusCount + this.token + this.remark + t1 + this.secretKey);
    }

    if (res.sign !== retSign) {
      this.dialog("签 名 错 误", 0, 0);
      threads.shutDownAll();
    }
    if (Math.abs(t - t1) > 60) {
      this.dialog("签 名 已 过 期", 0, 0);
      threads.shutDownAll();
    }

    if (this.mode === 0) {
      this.dialog("认证成功\n有效期到：" + endDate, 1, 0);
      console.log("认证成功，有效期到：" + endDate);
    } else {
      this.dialog("认证成功\n您还剩余 " + surplusCount + " 次可用次数", 1, 0);
      console.log("认证成功，您还剩余 " + surplusCount + " 次可用次数");
    }

    if (code !== 200) {
      threads.shutDownAll();
    }

    this.success = true; // 认证成功设置 this.success = true ，便于后面直接判断

    return res; // 认证成功返回服务器响应的所有信息，可根据需求使用，详见开发文档
  } else {
    if (code === 104) {
      let link = this.decrypt(res.link);
      this.changeDev(usr, pwd, link);
    } else {
      this.dialog(this.decrypt(res.msg), 0, 0);
    }

    threads.shutDownAll();
  }
};



/**
 * 心跳轮询，自动调用，无需理会
 */
Sdk.prototype.heartBeat = function () {
  let cycles;
  let count = 0;
  if (this.debug) {
    cycles = 3;
  } else {
    /** 循环次数不得低于20, 建议 50 - 100 为最佳！过于频繁访问服务器，你的IP将被暂时或永久封禁
         心跳验证频率过快，如果网络质量不好，还会导致脚本运行时容易中断，因为网络访问总有不确定因素 */
    cycles = 60; // 心跳循环次数 - 如果想调整心跳间隔，修改该值即可，不得低于20, 建议 50 - 100 为最佳
  }

  sleep(1000 * 10);
  do {
    let t = this.t();
    let apiRoute = "License/heartBeat/" + this.appId;
    let sign = this.sha1(
      apiRoute + this.token + this.deviceId + t + this.secretKey
    );
    let data = this.token + "|" + this.deviceId + "|" + t;
    let res = this.httpPost(apiRoute, data, sign);
    let code = Number(this.decrypt(res.code));
    console.log("心跳状态：" + code + "，当前心跳间隔 " + cycles * 10 + " 秒  " + this.time());
    if (code === 200) {
      count = 0;
      let retSign, endDate, surplusCount;
      let t1 = this.decrypt(res.timeStamp);

      if (this.mode === 0) {
        endDate = this.decrypt(res.endDate);
        retSign = this.sha1(code + endDate + t1 + this.secretKey);
      } else {
        surplusCount = this.decrypt(res.surplusCount);
        retSign = this.sha1(code + surplusCount + t1 + this.secretKey);
      }

      if (res.sign !== retSign || Math.abs(t - t1) > 60) {
        break;
      }

      for (let i = 0; i < cycles; i++) {
        sleep(1000 * 10);
        if (this.mode === 0) {
          if (this.t() - this.toTime(endDate) > 0) {
            console.log("授 权 已 过 期");
            count = 100;
            break;
          }
        }
      }
    } else {
      let arr = [100, 101, 102, 103, 201, 203, 502, 503, 504, 600, 601];
      if (arr.includes(code)) {
        console.log(this.decrypt(res.msg));
        break;
      } else {
        sleep(1000 * 30);
        count++;
      }
    }
  } while (count < 10);

  threads.shutDownAll();
};



/**
 * 用户换机综合接口，密码传入空值视为单卡模式，反之视为账号密码模式
 */
Sdk.prototype.changeDev = function (账号, 密码, 购卡网址) {
  let 换机码 = "";
  let wait = threads.disposable();
  if (this.changeMode === 0) {
    dialogs
      .build({
        canceledOnTouchOutside: false,
        title: "注 意：",
        titleColor: "#FF0000",
        contentColor: "#000066",
        content: "检测到您已在其他设备激活使用",
        positive: "【激活当前设备】",
        positiveColor: "#FF0000",
        negative: "【取消】",
        negativeColor: "#003000",
        type: "app-or-overlay",
      })
      .on("positive", () => {
        wait.setAndNotify(true);
      })
      .show();
    if (wait.blockedGet() == false) {
      threads.shutDownAll();
    }
  } else {
    dialogs
      .build({
        canceledOnTouchOutside: false,
        title: "注 意：",
        titleColor: "#FF0000",
        contentColor: "#000066",
        content: "检测到您已在其他设备激活使用",
        positive: "【我有换机码】",
        positiveColor: "#FF0000",
        negative: "【购买换机码】",
        negativeColor: "#003000",
        type: "app-or-overlay",
      })
      .on("positive", () => {
        wait.setAndNotify(true);
      })
      .on("negative", () => {
        wait.setAndNotify(false);
      })
      .show();
    if (wait.blockedGet() == true) {
      rawInput("输入您的换机码：", "", (text) => {
        换机码 = text.trim();
      });
      let fmt = /^[\w-]{10,20}$/;
      if (!fmt.test(换机码)) {
        toast("输 入 长 度 或 格 式 不 符 合 规 则");
        threads.shutDownAll();
      }
    } else {
      if (/^(([A-Za-z0-9:\/]+)\.)+([A-Za-z0-9\/])/.test(购卡网址)) {
        app.openUrl(购卡网址); /* 打开在线购卡网址 */
      }
      threads.shutDownAll();
    }
  }

  let t = this.t();
  let apiRoute = "Device/change/" + this.appId;
  let sign = this.sha1(apiRoute + this.changeMode + 账号 + 密码 + 换机码 + this.deviceId + t + this.secretKey);
  let data =
    this.changeMode + "|" + 账号 + "|" + 密码 + "|" + 换机码 + "|" + this.deviceId + "|" + t;
  let res = this.httpPost(apiRoute, data, sign);
  let code = Number(this.decrypt(res.code));
  if (code === 200) {
    this.dialog("换 机 成 功 , 请 重 新 运 行 程 序", 1);
  } else {
    this.dialog(this.decrypt(res.msg), 0);
  }
};



/**
 * 充值综合接口，使用新卡对已激活的旧卡或账号进行充值
 * @param 卡号或账号   需要增加时长的卡号或账号(已使用过)
 * @param 充值卡号      用于充值的卡号(未使用过)
 */
Sdk.prototype.recharge = function (卡号或账号, 充值卡号) {
  let authCode = 卡号或账号.trim();
  let newCard = 充值卡号.trim();
  let fmt = /^[\w.-]{5,30}$/;
  if (!fmt.test(authCode) || !fmt.test(newCard)) {
    toast("输 入 长 度 或 格 式 不 符 合 规 则");
    return {
      code: this.encrypt("203"),
      msg: this.encrypt("输 入 长 度 或 格 式 不 符 合 规 则"),
    };
  }
  let t = this.t();
  let apiRoute = "License/recharge/" + this.appId;
  let sign = this.sha1(apiRoute + authCode + newCard + t + this.secretKey);
  let data = authCode + "|" + newCard + "|" + t;
  let res = this.httpPost(apiRoute, data, sign);
  let code = Number(this.decrypt(res.code));
  if (code === 200) {
    if (this.mode === 0) {
      this.dialog(
        "充 值 成 功\n有 效 时 间 增 加 " +
        this.decrypt(res.duration) +
        " 分 钟",
        1
      );
    } else {
      this.dialog(
        "充 值 成 功\n可 用 次 数 增 加 " + this.decrypt(res.newCount) + " 次",
        1
      );
    }
  } else {
    this.dialog(this.decrypt(res.msg), 0);
  }

  return res;
};



/**
 * 用户修改密码
 * @param 账号    账号 5 - 20 位
 * @param 密码    密码 8 - 20 位
 * @param 新密码  新密码 8 - 20 位
 */
Sdk.prototype.changePassword = function (账号, 密码, 新密码) {
  let usr = 账号.trim();
  let pwd = 密码.trim();
  let npwd = 新密码.trim();
  let fmt = /^[\w.]{5,20}$/;
  if (!fmt.test(usr) || !fmt.test(pwd) || !fmt.test(npwd)) {
    toast("输 入 长 度 或 格 式 不 符 合 规 则");
    return {
      code: this.encrypt("203"),
      msg: this.encrypt("输 入 长 度 或 格 式 不 符 合 规 则"),
    };
  }
  let t = this.t();
  let apiRoute = "License/changePassword/" + this.appId;
  let sign = this.sha1(apiRoute + usr + pwd + npwd + t + this.secretKey);
  let data = usr + "|" + pwd + "|" + npwd + "|" + t;
  let res = this.httpPost(apiRoute, data, sign);
  let code = Number(this.decrypt(res.code));
  if (code === 200) {
    this.dialog("改 密 成 功\n新 密 码 : " + npwd, 1);
  } else {
    this.dialog(this.decrypt(res.msg), 0);
  }

  return res;
};



/**
 * 注册与试用综合接口，账号密码为空视为获取试用卡号，反之视为注册账号
 * @param 账号    账号 5 - 20 位
 * @param 密码    密码 8 - 20 位
 */
Sdk.prototype.regOrTest = function (账号, 密码) {
  let data;
  let usr = 账号.trim();
  let pwd = 密码.trim();
  let t = this.t();
  let apiRoute = "Trial/getCard/" + this.appId;

  if (usr === "") {
    data = this.mode + "|" + this.deviceId + "|" + t;
  } else {
    let fmt = /^[\w.]{5,20}$/;
    let fmt1 = /^[\w.]{8,20}$/;
    if (!fmt.test(usr) || !fmt1.test(pwd)) {
      toast("输 入 长 度 或 格 式 不 符 合 规 则");
      return {
        code: this.encrypt("203"),
        msg: this.encrypt("输 入 长 度 或 格 式 不 符 合 规 则"),
      };
    }
    data = this.mode + "|" + usr + "|" + pwd + "|" + this.deviceId + "|" + t;
  }

  let sign = this.sha1(
    apiRoute + this.mode + usr + pwd + this.deviceId + t + this.secretKey
  );
  let res = this.httpPost(apiRoute, data, sign);
  // let code = Number(this.decrypt(res.code));
  // if (code === 200) {
  //   if (this.mode === 0) {
  //     if (usr === '') {
  //       setClip(this.decrypt(res.authCode));
  //       this.dialog(
  //         '试用卡号：\n' +
  //           this.decrypt(res.authCode) +
  //           '\n可免费使用到：\n' +
  //           this.decrypt(res.endDate) +
  //           '\n您已获取 ' +
  //           this.decrypt(res.usedCount) +
  //           ' 次试用  还能获取 ' +
  //           (this.decrypt(res.trialCount) - this.decrypt(res.usedCount)) +
  //           ' 次',
  //         1
  //       );
  //     } else {
  //       let trialTime = this.decrypt(res.trialTime);
  //       if (trialTime > 0) {
  //         this.dialog('注 册 成 功\n账 号：' + this.decrypt(res.username) + '\n密 码：' + this.decrypt(res.password) + '\n系 统 赠 送 ' + trialTime + ' 分 钟 体 验 时 间', 1);
  //       } else {
  //         this.dialog('注 册 成 功\n账 号：' + this.decrypt(res.username) + '\n密 码：' + this.decrypt(res.password), 1);
  //       }
  //     }
  //   } else {
  //     let surplusCount = this.decrypt(res.surplusCount);
  //     if (usr === '') {
  //       setClip(this.decrypt(res.authCode));
  //       this.dialog(
  //         '试用卡号：\n' +
  //           this.decrypt(res.authCode) +
  //           '\n系统免费赠送 ' +
  //           surplusCount +
  //           ' 次使用机会\n您已获取 ' +
  //           this.decrypt(res.usedCount) +
  //           ' 次试用  还能获取 ' +
  //           (this.decrypt(res.trialCount) - this.decrypt(res.usedCount)) +
  //           ' 次',
  //         1
  //       );
  //     } else {
  //       if (surplusCount > 0) {
  //         this.dialog('注 册 成 功\n账 号：' + this.decrypt(res.username) + '\n密 码：' + this.decrypt(res.password) + '\n系 统 赠 送 ' + surplusCount + ' 次 使 用 机 会', 1);
  //       } else {
  //         this.dialog('注 册 成 功\n账 号：' + this.decrypt(res.username) + '\n密 码：' + this.decrypt(res.password), 1);
  //       }
  //     }
  //   }
  // } else if (code === 206) {
  //   this.dialog('操 作 失 败 , 已 达 注 册 或 试 用 上 限', 0);
  // } else {
  //   toast(this.decrypt(res.msg));
  // }

  return res;
};



/**
 * 在线更新 当后台设置的项目版本大于本地版本时，提示用户更新并跳转到下载网址
 * 如果点击确定按钮将停止脚本并打开下载网站，如果点击取消或不需要更新将返回项目配置信息，可以在后续调用
 */
Sdk.prototype.checkUpdate = function () {
  let res = this.appInfo(0);
  if (Number(this.decrypt(res.code)) === 200) {
    if (this.decrypt(res.version) - this.version > 0) {
      let fmt = /^(([A-Za-z0-9:\/]+)\.)+([A-Za-z0-9\/])/;
      let link = this.decrypt(res.downloadLink);
      if (this.updateMode === 0) {
        let result = this.dialog(
          "检测到新版\n点击【取 消】忽略\n点击【确 定】打开下载网址",
          1,
          1
        );
        if (result == true) {
          if (fmt.test(link)) {
            app.openUrl(link);
            exit();
          } else {
            this.dialog("下 载 网 址 未 配 置 或 格 式 错 误", 0);
          }
        }
      } else {
        this.dialog("检 测 到 新 版\n请 下 载 新 版 使 用", 1);
        if (fmt.test(link)) {
          app.openUrl(link);
          exit();
        } else {
          this.dialog("下 载 网 址 未 配 置 或 格 式 错 误", 0);
        }
      }
    } else {
      console.log("暂 无 更 新");
    }

    return res;
  }
};



/* ---------------------------------------------------------------------------------------------------------
                                   ↑ ↑ ↑ 以上是网络验证 SDK ↑ ↑ ↑
------------------------------------------------------------------------------------------------------------
                                     ↓ ↓ ↓以下为主脚本区域↓ ↓ ↓
--------------------------------------------------------------------------------------------------------- */
var sdk; // 首先定义一个全局变量
let config = storages.create("sdk");

var myApp = {};
myApp.title = "问天一号" //脚本名称
myApp.packageName = "com.sdu.didi.psnger" //程序包名
myApp.appVersion = "6.8.4" //app版本
myApp.characteristic="wentian"  //存储标识

myApp.mainMenu = 0  //下拉菜单索引
myApp.delayMin = "1000" //随机延迟最小值 毫秒
myApp.delayMax = "3000" //随机延迟最大值 毫秒
myApp.OrderAmount = "15" //订单最低金额   大于用户设置的金额开始接单
myApp.LocalOrder = "1" //  1：市内订单  2:跨城市订单  3:常用路线订单
myApp.CarrsMill = "30" //订单最低总距离   大于用户设置的距离开始接单
myApp.pax = "愿拼" // 愿拼 独享 下拉菜单索引 用户设置
myApp.RiderShip = "1"   //乘客人数
myApp.AutoRefreshCheckbox = true   //自动刷新
myApp.AutomaticGlide = true    //自动下滑

myApp.seat = 1   //车主座位数  下拉菜单索引
myApp.peer = "否"   //是否同行


showLoginUI();

function showLoginUI() {
  ui.layout(

    /* UI界面 */
    <vertical>

      {/* 创建顶部标题 */}
    <appbar>
      <toolbar title="问天一号 Test内部测试版:V0.1" />
        <tabs id="t1"/>
    </appbar>
    

      <linear>
          <Switch id="autoService" text="无障碍服务:" checked="{{auto.service != null}}" w="auto" textStyle="bold" />
          <Switch id="悬浮窗权限" text="悬浮窗权限" checked="{{floaty.checkPermission() != false}}" padding="8 8 8 8" textSize="15sp"/>
      </linear>
      
      <viewpager id="v1">
        {/* 用户激活启动界面 */}
        <vertical>
            <frame>
          <vertical h="auto" align="center" margin="20 50 20 120">
            <linear>
              <text
                w="75"
                gravity="left"
                color="#292421"
                size="16"
                text="卡  号："
              />
            </linear>
            <linear>
              <input id="card" w="*" h="40" size="15" />
            </linear>
            <linear weightSum="4">
              <linear gravity="left" layout_weight="3">
                <text
                  w="70"
                  gravity="right"
                  color="#292421"
                  size="16"
                  text="认证线路"
                />
                <spinner id="sp1" entries=" 1 | 2 | 3 " />
              </linear>
              <linear gravity="right" layout_weight="1">
                <button
                  id="clear"
                  h="35"
                  text="Clear"
                  bg="#006600"
                  size="12"
                  margin="0 10 0 2"
                  style="Widget.AppCompat.Button.Colored"
                />
              </linear>
            </linear>
            <linear id="h1" gravity="center">
              <button
                id="start"
                h="50"
                w="*"
                text="启 动 / Start"
                bg="#006600"
                size="18"
                textStyle="bold"
                margin="0 0 0 2"
                style="Widget.AppCompat.Button.Colored"
              />
            </linear>
            <linear id="h2" gravity="center">
              <button
                id="stop"
                h="50"
                w="*"
                text="停 止 / Stop"
                bg="#082E54"
                size="18"
                textStyle="bold"
                margin="0 0 0 2"
                style="Widget.AppCompat.Button.Colored"
              />
            </linear>
            <linear gravity="center" weightSum="3">
              <button
                id="contact"
                h="40"
                layout_weight="1"
                text="客  服"
                bg="#7cbb00"
                size="16"
                margin="0 0 0 2"
                style="Widget.AppCompat.Button.Colored"
              />
              <button
                id="shop"
                h="40"
                layout_weight="1"
                text="购  卡"
                bg="#f65314"
                size="16"
                margin="0 0 0 2"
                style="Widget.AppCompat.Button.Colored"
              />
              <button
                id="test"
                h="40"
                layout_weight="1"
                text="试  用"
                bg="#ffbb01"
                size="16"
                margin="0 0 0 2"
                style="Widget.AppCompat.Button.Colored"
              />
            </linear>
            <linear gravity="center">
              <text
                id="gg1"
                textStyle="bold"
                padding="0 15 0 5"
                color="#f65314"
                size="18"
                text=""
              />
            </linear>

            <linear gravity="center">
              <text id="gg2" textStyle="bold" color="#0C090A" size="16" text="" />
            </linear>
          </vertical>
        </frame>
        </vertical>

        {/* 滴滴功能区界面 */}
        <vertical>
          <card w="*" h="150" margin="10 5" cardCornerRadius="2dp"
              cardElevation="1dp" gravity="center_vertical">  
          <vertical padding="18 8" h="auto">
              <linear>
                  <horizontal>
                      <text textSize="16sp">功能选择</text>
                      <spinner id="mainMenu" entries="滴滴顺风车抢单|哈啰顺风车抢单 开发中选中无效" />
                  </horizontal>
              </linear>

              <linear>
                  <horizontal>
                      <checkbox id="AutoRefreshCheckbox" text="自动刷新" gravity="center_vertical" checked="true" />
                      <checkbox id="AutomaticGlide" text="自动下滑" gravity="center_vertical" checked="true" />
                  </horizontal>
              </linear>

              <linear>
                  <horizontal>
                      <text text="下拉速度最小值:   " textColor="black" w="auto" />
                      <input id="delayMin" color="black" inputType="number" w="30" />
                      <text text="毫秒 到  最大值:" textColor="black" w="auto" />
                      <input id="delayMax" color="black" inputType="number" w="30" />
                      <text text="毫秒" textColor="black" w="auto" /> 
                  </horizontal>
              </linear>

          </vertical>
          </card>


          <vertical>
              <scroll>     {/* 创建下滑滚动框 */}
                <vertical>

                <card w="*" h="100" margin="10 5" cardCornerRadius="2dp"
                      cardElevation="1dp" gravity="center_vertical">       
                  <vertical padding="18 8" h="auto">
                      <linear>
                          {/* <text text="车主设置 可供座位数:" textColor="black" w="auto" />
                          <input id="seat" color="black" inputType="number" w="60" />
                          <text text="座" textColor="black" w="auto" /> */}
                          <horizontal>
                            <text textSize="16sp">车主设置 可供座位数:</text>
                            <spinner id="seat" entries="1座|2座|3座|4座" />
                          </horizontal>
                      </linear>

                      <linear>
                          <horizontal>
                              <text textSize="16sp">是否有亲友同行</text>
                              <spinner id="mainMenu" entries="是|否" />
                          </horizontal>
                      </linear>
                    </vertical>
                </card>

                <card w="*" h="70" margin="10 5" cardCornerRadius="2dp"
                      cardElevation="1dp" gravity="center_vertical">       
                  <vertical padding="18 8" h="auto">
                      <linear>
                          <text text="订单最低金额:" textColor="black" w="auto" />
                          <input id="OrderAmount" color="black" inputType="number" w="60" />
                          <text text="元，抢单-" textColor="black" w="auto" />
                          <text text="注:默认值为：0" textColor="red" w="auto" />
                      </linear>
                    </vertical>
                </card>



                  <card w="*" h="150" margin="10 5" cardCornerRadius="2dp"
                      cardElevation="1dp" gravity="center_vertical"> 
                  <vertical padding="18 8" h="auto">
                      <linear>

                          <text text="订单最低距离:" textColor="black" w="auto" />
                          <input id="CarrsMill" color="black" inputType="number" w="60" />
                          <text text="km" textColor="black" w="auto" />
                      </linear>

                      <linear>
                          <text text="乘客人数:" textColor="black" w="auto" />
                          <input id="RiderShip" color="black" inputType="number" w="30" />

                          <horizontal>
                              <text textSize="16sp">拼车:</text>
                              <spinner id="pax" entries="愿拼|独享" />
                          </horizontal>
                      </linear>
                  </vertical>
                </card>

                </vertical>
              </scroll>
            </vertical>


        </vertical>
        {/* 哈啰功能区 */}
        <vertical>
          <card w="*" h="70" margin="10 5" cardCornerRadius="2dp"
                cardElevation="1dp" gravity="center_vertical">       
            <vertical padding="18 8" h="auto">
                <linear>
                    <horizontal>
                        <text textSize="16sp">功能选择</text>
                        <spinner id="mainMenu" entries="滴滴顺风车抢单|哈啰顺风车抢单 开发中选中无效" />
                    </horizontal>
                </linear>
            </vertical>
            </card>


            <card w="*" h="70" margin="10 5" cardCornerRadius="2dp"
                cardElevation="1dp" gravity="center_vertical">       
            <vertical padding="18 8" h="auto">
                <linear>
                    <text text="订单最低金额:" textColor="black" w="auto" />
                    <input id="OrderAmount" color="black" inputType="number" w="60" />
                    <text text="元，抢单-" textColor="black" w="auto" />
                    <text text="注:默认值为：0" textColor="red" w="auto" />
                </linear>
            </vertical>
            </card>
        </vertical>
    </viewpager>

    </vertical>
  );

  ui.h2.visibility = 200;

  getDate(true);  //读取界面配置

  /* 读取配置 */
  let val = config.get("card");
  if (val != null) {
    ui.card.setText(val);
  }
  val = config.get("sp1");
  if (val != null) {
    ui.sp1.setSelection(val);
  }

  /* 清除配置 */
  ui.clear.click(function () {
    config.clear();
    showLoginUI();
    toast("配 置 已 清 除");
  });

  /* 停止脚本 */
  ui.stop.click(function () {
    ui.h1.visibility = 0;
    ui.h2.visibility = 200;
    threads.shutDownAll();
  });

  // 无障碍服务开关单击事件
ui.autoService.on("check",function(checked){
  // 用户勾选无障碍服务的选项时，跳转到页面让用户去开启
  if(checked && auto.service == null){
      app.startActivity({
          action:"android.settings.ACCESSIBILITY_SETTINGS"
      });
  }
  if(!checked && auto.service != null){
      auto.service.disableSelf();
  }
});

ui.悬浮窗权限.on("check", function(checked) {
  //申请悬浮窗
  importClass(android.content.Intent);
  importClass(android.net.Uri);
  importClass(android.provider.Settings);
  var intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
      Uri.parse("package:" + context.getPackageName()));
  intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
  app.startActivity(intent);
});

ui.emitter.on("resume",function(){
  //此时根据无障碍服务的开启情况，同步开关的状态
  ui.autoService.check = auto.service != null;
  ui.悬浮窗权限.checked = floaty.checkPermission() != false
});

// 顶部标题设置
ui.v1.setTitles(["激活","滴滴","哈啰开发中..."])
ui.t1.setupWithViewPager(ui.v1)


  /* 检测更新 与 远程公告 */
  threads.start(function () {
    sdk = new Sdk(); // 调用网络验证SDK，由于前面设置了 sdk 为全局变量，因此后续调用相关方法无需再 new
    let res = sdk.checkUpdate(0); // 检测更新 并 返回项目配置信息
    if (Number(sdk.decrypt(res.code)) === 200) {
      log(
        "---------------这部分代码目的是演示如何得到这些参数，可删除没有被使用的部分---------------"
      );
      let 项目名称 = sdk.decrypt(res.appName);
      log("项目名称:" + 项目名称);
      let 后台版本 = Number(sdk.decrypt(res.version));
      log("后台版本:" + 后台版本);
      let 项目开关 = Number(sdk.decrypt(res.appStatus));
      log("项目开关:" + 项目开关);
      let 公告开关 = Number(sdk.decrypt(res.noticeStatus));
      log("公告开关:" + 公告开关);
      sdk.noticeStatus = 公告开关;

      let 试用次数 = Number(sdk.decrypt(res.trialCount));
      log("试用次数:" + 试用次数);
      let 试用时长 = Number(sdk.decrypt(res.trialTime));
      log("试用时长:" + 试用时长);
      let 顶号扣时 = Number(sdk.decrypt(res.changeTime));
      log("顶号扣时:" + 顶号扣时);

      let 官方网址 = sdk.decrypt(res.webSite);
      log("官方网址:" + 官方网址);
      let 专属网店 = sdk.decrypt(res.shopLink);
      log("专属网店:" + 专属网店);
      sdk.shopLink = 专属网店;

      let 下载网址 = sdk.decrypt(res.downloadLink);
      log("下载网址:" + 下载网址);

      let 公告信息 = sdk.decrypt(res.notice);
      log("公告信息:" + 公告信息);
      sdk.notice = 公告信息;

      let 客服信息 = sdk.decrypt(res.contactInfo);
      log("客服信息:" + 客服信息);
      sdk.contactInfo = 客服信息;
      log(
        "------------------------------------------------------------------------"
      );

      /* --------------------------------------------------------------------------------------------------------
            远程公告
            --------------------------------------------------------------------------------------------------------- */
      if (公告开关 === 1 && 公告信息 !== "") {
        ui.run(() => {
          ui.gg1.setText("官 方 公 告");
          ui.gg2.setText(公告信息.replace(/\|/g, "\n"));
        });
      }
    }
  });

  /* ---------------------------------------------------------------------------------------------------------
      手动选择最佳线路
    --------------------------------------------------------------------------------------------------------- */
  ui.sp1.setOnItemClickListenerInt(function () {
    sdk.manualSwitch();
    console.log("当前API：" + sdk.api);
  });

  /* --------------------------------------------------------------------------------------------------------
      在线购卡
    --------------------------------------------------------------------------------------------------------- */
  // ui.shop.click(function () {
  //   let fmt = /^(([A-Za-z0-9:\/]+)\.)+([A-Za-z0-9\/])/;
  //   threads.start(function () {
  //     if (fmt.test(sdk.shopLink)) {
  //       app.openUrl(sdk.shopLink);
  //     } else {
  //       toast("官 方 网 址 未 配 置 或 格 式 错 误");
  //     }
  //   });
  // });

  /* --------------------------------------------------------------------------------------------------------
    联系客服 - 以下代码为发起QQ对话示例 - 后台联系信息输入QQ号即可
    --------------------------------------------------------------------------------------------------------- */
  ui.contact.click(function () {
    threads.start(function () {
      sdk.qqMessage(sdk.contactInfo);
    });
  });

  /* --------------------------------------------------------------------------------------------------------
        取试用卡号
    --------------------------------------------------------------------------------------------------------- */
  ui.test.click(function () {
    threads.start(function () {
      let res = sdk.regOrTest("", "");
      let code = Number(sdk.decrypt(res.code));
      if (code == 200) {
        let card = sdk.decrypt(res.authCode);
        ui.run(() => {
          ui.card.setText(card);
          config.put("card", card);
        });
        if (sdk.mode === 0) {
          sdk.dialog(
            "试用卡号：\n" +
            card +
            "\n可免费使用到：\n" +
            sdk.decrypt(res.endDate),
            1
          );
          sdk.dialog(
            "卡号已自动置入卡号输入框\n" +
            "您已获取 " +
            sdk.decrypt(res.usedCount) +
            " 次试用  还能获取 " +
            (sdk.decrypt(res.trialCount) - sdk.decrypt(res.usedCount)) +
            " 次",
            1
          );
        } else {
          sdk.dialog(
            "试用卡号：\n" +
            card +
            "\n系统免费赠送 " +
            sdk.decrypt(res.surplusCount) +
            " 次使用机会",
            1
          );
          sdk.dialog(
            "卡号已自动置入卡号输入框\n" +
            "您已获取 " +
            sdk.decrypt(res.usedCount) +
            " 次试用  还能获取 " +
            (sdk.decrypt(res.trialCount) - sdk.decrypt(res.usedCount)) +
            " 次",
            1
          );
        }
      } else if (code === 206) {
        sdk.dialog("操 作 失 败 , 已 达 注 册 或 试 用 上 限", 0);
      } else {
        toast(sdk.decrypt(res.msg));
      }
    });
  });

  /* ---------------------------------------------------------------------------------------------------------
    卡号认证 认证失败停止所有线程，认证成功返回响应的所有信息 并赋值 sdk.success = true
    ------------------------------------------------------------------------------------------------------------
    如果检测到用户换机，那么自动调用换机相关方法，因此换机相关功能您无需编写额外的代码
    --------------------------------------------------------------------------------------------------------- */
  ui.start.click(function () {
    threads.shutDownAll();
    config.put("sp1", ui.sp1.getSelectedItemPosition());
    threads.start(function () {
      let card = ui.card.text();
      sdk.verify(card, ""); // 如果第二个参数为空，视为单卡号认证，反之视为账号密码认证
      if (sdk.success) {
        config.put("card", card);

        log("激活成功")
        saveDate();  //保存界面配置     调用这个方法出现报错
        getDate(false);  //读取界面配置

        //程序开始运行之前判断无障碍服务
        if(auto.service == null){
            toastLog("请先开启无障碍服务!");
            return;
        };
    
        if (floaty.checkPermission() == false) {
            toast("请先开启悬浮窗权限！")
            return;
        };

        ui.run(() => {
          ui.h1.visibility = 200;
          ui.h2.visibility = 0;
        });

        /* 认证成功，启动心跳轮询线程 */
        threads.start(function () {
          sdk.heartBeat();
        });
        threads.start(function(){
          //在新线程执行的代码
          main();
      });

      function main(){
        if(myApp.mainMenu == "0"){
            app.launch(myApp.packageName)  //启动滴滴
            waitForActivity("com.didi.sdk.app.MainActivity")  //等待进入滴滴主界面
            App_滴滴获取订单();
        }else if(myApp.mainMenu == "1"){
            log("哈啰顺风车开发中")
        };
    };

    // 封装函数
  function App_滴滴获取订单() { //已完成
      while (true) {
          //log(myApp.pax)
          if (id("com.sdu.didi.psnger:id/sfc_home_end_address_tv").text("你要去哪儿").exists()) {
              //toastLog("当前在接单界面")
              if(myApp.AutomaticGlide === true){
                swipe(device.width / 2, device.height * 3 / 4, device.width / 2, device.height / 4, 500); //下滑500毫米
              };
              

              /* 遍历订单列表  金额 */
              var sum = id('com.sdu.didi.psnger:id/sfc_order_price_content').find(); //获取订单金额
              var distance1 = id('com.sdu.didi.psnger:id/sfc_order_card_tips_content').find(); //获取订单 乘客人数 愿拼 订单总路程 
              
              sum.forEach((child, index) => {
                  var num = child.text()
                  var numa = parseFloat(num.match(/\d+(\.\d+)?/)[0]); //使用正则表达式获取订单金额
  
                  sleep(random(parseInt(myApp.delayMin), parseInt(myApp.delayMax))) //随机延迟  * 1000等于秒

                  if (numa > myApp.OrderAmount || myApp.OrderAmount == 0) { // 判断numa的金额是否满足条件  numa由前端用户传入 默认值为:0
  
                      /* 用户设置金额满足条件后开始判断距离 */
                      distance1.forEach((child,index) =>{
                          var num = child.text()
                          var Passenger = parseFloat(num.match(/\d+(\.\d+)?/)[0]); //使用正则表达式获取乘客人数
                          var CarPooling = num.match(/[\u4e00-\u9fff]+/);   //使用正则表达式获取 “独享” “愿拼”
                          var result =(CarPooling && CarPooling[0].slice(1))? (CarPooling[0].slice(1)).toString() : '';//numb? (numb[0].slice(1)).toString() : '';//使用正则表达式获取 “独享” “愿拼”
                          var distance = num.match(/\d+\.\d+/)[0]; 
                          //log("金额符合---"+"乘客人数:"+Passenger+"拼车"+result+"距离："+distance);
                          log("乘客人数:"+myApp.RiderShip+myApp.pax+"总距离:"+myApp.CarrsMill)
                          log(Passenger+result+distance)
                          if(Passenger == myApp.RiderShip && result == myApp.pax && distance >= parseFloat(myApp.CarrsMill)){ 
                              log("满足条件开始接单:---"+"乘客人数："+Passenger+"拼车："+result+"---订单总距离:"+distance)     /* 当前默认值myApp.pax："愿拼"    myApp.CarrsMill：0 */
                              id('com.sdu.didi.psnger:id/sfc_order_card_tips_content').findOne().parent().parent().parent().parent().click();  //点击匹配的订单
                              id('com.sdu.didi.psnger:id/btn_main_title').findOne().parent().parent().click();  //点击邀请同行
                              /* 明天 添加用户设置车辆信息和 获取订单时间 */
                              App_滴滴开始接单();

                          }else if(id('com.sdu.didi.psnger:id/bt_refresh').exists() && myApp.Glide === 1){
                              //执行判断是否在车主界面
                              swipe(device.width / 2, device.height * 3 / 4, device.width / 2, device.height / 4, 500);
                              log(myApp.RiderShip+"---"+result+"---"+distance+"---");
                              log(myApp.pax+"---"+myApp.CarrsMill);
                          };
                      });
                      /* ------------------------------------------------------------------------------------------- */
                  } else if(myApp.AutomaticGlide === true){
                      swipe(device.width / 2, device.height * 3 / 4, device.width / 2, device.height / 4, 500); //向下滑动5毫米  刷新按钮控件ID：fullId('com.sdu.didi.psnger:id/bt_refresh')
                      log("金额不符合"+myApp.OrderAmount+myApp.AutomaticGlide)
  
                      /* 下面的代码是下滑3次后如果 */
                  //     let scrollCount = 0;
                  //     while (scrollCount < 3){
                  //          // 执行下滑操作
                  //             swipe(device.width / 2, device.height * 3 / 4, device.width / 2, device.height / 4, 500);
                  //             scrollCount++;
                  //             // 下滑 3 次后点击刷新按钮
                  //             id('com.sdu.didi.psnger:id/bt_refresh').findOne().click();
                  //     };
                  };
              });
          }else if(id("com.sdu.didi.psnger:id/home_search_box_end_address_text").exists()){   // 如果用户在首页将点击车主按钮
              id("com.sdu.didi.psnger:id/normal_icon").findOne().parent().parent().click();  //点击车主按钮
          }else if(id("com.sdu.didi.psnger:id/popClose").exists()){
              id('com.sdu.didi.psnger:id/popClose').findOne().parent().click()  //关闭弹窗
          };
      };
  };


  function App_滴滴开始接单(){

    if(myApp.seat === 0){
      sleep(1000)
      let b = "5座"
      let a = id('com.sdu.didi.psnger:id/grid_view_item').find();
      for (let item of a) {
          if (item.text() === "1座") {
              item.click();
              log("找到 "+ b + "座控件");
              break;
          };
      }

    }


    log("开始接单"+"用户选择座位数:"+myApp.seat)
  };


        /* ---------------------------------------------------------------------------------------------------------
        清除以下内容，把你的脚本写入/替换到这里即可
        如果您的脚本包含界面，那么把界面封装到函数，调用该函数即可呼出您原本的界面

        超级防破解实现 :
        1.关键性参数使用远程变量读写
        2.涉及计算、拼接相关的代码封装成远程函数在云端计算
        云端数据的每一次调用都需要签名和时间鉴权，因此不管是抓包还是爆破都没有任何用处
         --------------------------------------------------------------------------------------------------------- */
        // let 函数结果 = sdk.runFunction('远程函数示例', '100, 200');
        // console.log('远程函数执行的结果是：' + 函数结果 + '\n ')
        
        
        
        /* 相关方法调用示例：
        console.log("这是卡密备注的信息：" + sdk.remark);
        sdk.setRemark(card, '新的卡密备注信息'); // 更新卡密备注信息
        sdk.addVal('newVal01', '变量的值', '备忘信息'); // 添加远程变量
        let value = sdk.getValue("netVal005"); // 读取远程变量
        console.log("远程变量：" + value);
        sdk.setValue("netVal006", "远程变量更新测试"); // 更新远程变量 */
        
        let i = 0;
        while (i < 3600) {
          sleep(5000);
          i = i + 5;
          toast("问天一号 内部测试版 Test:V0.1 已 运 行【" + i + "】秒");
        }
      } else {
        threads.shutDownAll();
      }
    });
  });
//保存界面配置
function saveDate(){
  //ui.mainMenu.getSelectedltemPosition()   获取下拉菜单索引
  setStorageData(myApp.characteristic,"mainMenu",ui.mainMenu.getSelectedItemPosition())   
  setStorageData(myApp.characteristic,"pax",ui.pax.getSelectedItemPosition()) //myApp.pax
  setStorageData(myApp.characteristic,"seat",ui.mainMenu.getSelectedItemPosition())  //保存 车主座位数 myApp.seat


  setStorageData(myApp.characteristic,"OrderAmount",ui.OrderAmount.text())    //获取金额输入框的值
  setStorageData(myApp.characteristic,"CarrsMill",ui.CarrsMill.text())    //获取订单距离输入框的值
  setStorageData(myApp.characteristic,"delayMin",ui.delayMin.text())    //获取刷新速度最小值
  setStorageData(myApp.characteristic,"delayMax",ui.delayMax.text())    //获取刷新速度最小值
  setStorageData(myApp.characteristic,"RiderShip",ui.RiderShip.text())    //获取人数  RiderShip

  setStorageData(myApp.characteristic,"AutoRefreshCheckbox",ui.AutoRefreshCheckbox.isChecked()) //存储下滑选择框是否选中
  setStorageData(myApp.characteristic,"AutomaticGlide",ui.AutomaticGlide.isChecked()) //存储刷新选择框是否选中
};


/* 已知bug 调用 isSetVlue时程序在打包之后直接崩溃*/
// //读取界面配置
// //isSetVlue:是否设置ui组件的值   逻辑型参数
function getDate(isSetVlue){
  // 判断本地是否存储菜单的索引值。
  if(getStorageData(myApp.characteristic,"mainMenu") != undefined){
      // 重新定义菜单索引
      myApp.mainMenu = getStorageData(myApp.characteristic,"mainMenu")
      // 设置菜单选中项目
      isSetVlue && ui.mainMenu.setSelection(myApp.mainMenu);

      // if(isSetVlue){
      //     ui.mainMenu.setSelection(myApp.mainMenu);
      // }
  };
  if(getStorageData(myApp.characteristic,"seat") != undefined){
    myApp.seat = getStorageData(myApp.characteristic,"seat")
  };
  isSetVlue && ui.seat.setSelection(myApp.seat);

  if(getStorageData(myApp.characteristic,"OrderAmount") != undefined){
    myApp.OrderAmount = getStorageData(myApp.characteristic,"OrderAmount");
  };
  isSetVlue && ui.OrderAmount.setText(myApp.OrderAmount);

  if(getStorageData(myApp.characteristic,"CarrsMill") != undefined){
    myApp.CarrsMill = getStorageData(myApp.characteristic,"CarrsMill")
  };
  isSetVlue && ui.CarrsMill.setText(myApp.CarrsMill);

  if(getStorageData(myApp.characteristic,"delayMin") != undefined){
    myApp.delayMin = getStorageData(myApp.characteristic,"delayMin")
  };
  isSetVlue && ui.delayMin.setText(myApp.delayMin);

  if(getStorageData(myApp.characteristic,"delayMax") != undefined){
    myApp.delayMax = getStorageData(myApp.characteristic,"delayMax")
  };
  isSetVlue && ui.delayMax.setText(myApp.delayMax);

  if(getStorageData(myApp.characteristic,"RiderShip") != undefined){
    myApp.RiderShip = getStorageData(myApp.characteristic,"RiderShip")
  };
  isSetVlue && ui.RiderShip.setText(myApp.RiderShip);

  /* 判断自动刷新选择框是否被选中 */
  if(getStorageData(myApp.characteristic,"AutoRefreshCheckbox") != undefined){
    myApp.AutoRefreshCheckbox = getStorageData(myApp.characteristic,"AutoRefreshCheckbox")
  };
  isSetVlue && ui.AutoRefreshCheckbox.setChecked(myApp.AutoRefreshCheckbox);

  if(getStorageData(myApp.characteristic,"AutomaticGlide") != undefined){
    myApp.AutomaticGlide = getStorageData(myApp.characteristic,"AutomaticGlide")
  };
  isSetVlue && ui.AutomaticGlide.setChecked(myApp.AutomaticGlide);

};


//保存本地数据
function setStorageData(name, key,value){
  const storage=storages.create(name); //创建storage对象
  storage.put(key, value);
};

//读取本地数据
function getStorageData(name, key){
  const storage=storages.create(name);//创建storage对象
  if(storage.contains(key)){
      return storage.get(key, "")
  };
  //默认返回undefined
};

//删除本地数据
function delstorageData(name, key){
  const storage =storages.create(name); //创建storage对象
  if(storage.contains(key)){
      storage.remove(key);
  };
};
};
