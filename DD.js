// "ui";
// ui.layout(
//     <vertical padding="16">
//         <horizontal>
//             <text textSize="16sp">下拉菜单</text>
//             <spinner id="sp1" entries="选项1|选项2|选项3"/>
//         </horizontal>
//         <horizontal>
//             <text textSize="16sp">对话框菜单</text>
//             <spinner id="sp2" entries="选项4|选项5|选项6" spinnerMode="dialog"/>
//         </horizontal>
//         <button id="ok">确定</button>
//         <button id="select3">选择选项3</button>
//     </vertical>
// );

// ui.ok.on("click", ()=>{
//     var i = ui.sp1.getSelectedItemPosition();
//     var j = ui.sp2.getSelectedItemPosition();

//     if(i === 0){
//         log("a")
//     }else if(i === 1){
//         log("b")
//     }else if(i === 2){
//         log("c")
//     }else if(i === 3){
//         log("d")
//     }

//     toast("您的选择是选项" + (i + 1) + "和选项" + (j + 4));
// });

// ui.select3.on("click", ()=>{
//     ui.sp1.setSelection(2);
// });

       //查找控件
       let b = "3座"
       let a = id('com.sdu.didi.psnger:id/grid_view_item').find();
       for (let item of a) {
           if (item.text() === b) {
               item.click();
               log("找到 "+ b + "控件");
               break;
            }
       }

// console.show();

// log(currentActivity())


// "ui";

// ui.layout(
//     <ScrollView>
//     <vertical>
//         <appbar>
//             <toolbar id="toolbar" title="问天一号"/>
//         </appbar>
//         <card w="*" h="70" margin="10 5" cardCornerRadius="2dp"
//             cardElevation="1dp" gravity="center_vertical">
//         <vertical padding="18 8" h="auto">
//             <linear>
//                 <text text="计划运行:" textColor="black" w="auto" />
//                 <input id="totalNum" color="black" inputType="number" w="60" />
//                 <text text="个后，停止" textColor="black" w="auto" />
//             </linear>
//         </vertical>
//         <View bg="#f44336" h="*" w="10" />
//         </card>
//     </vertical>
//     </ScrollView>
// );





// //log(id(com.sdu.didi.psnger:id/sfc_order_price_content).visibleToUser(true));


// // var like = id('com.sdu.didi.psnger:id/sfc_order_price_content').findOne().bounds();

// // var x = like.centerX();

// // var y =like.centerY();

// // click(x,y)

// /* 完成一个的点击任务 */
// // var like = id('com.sdu.didi.psnger:id/sfc_order_price_content').findOne();
// // //log(like.parent().parent().parent().parent().click());
// // log(like.text())

// //log(like.parent().parent().parent().parent().click());

// /* 遍历订单列表 */
// var like = id('com.sdu.didi.psnger:id/sfc_order_price_content').find();

// like.forEach((child,index) => {
//     // log(child.text() +"-----"+index);

//     child.parent().parent().parent().parent().click();

//     id('com.sdu.didi.psnger:id/sfc_invite_dev_title_bar').waitFor();

//     sleep(1000);

//     back();

//     id('com.sdu.didi.psnger:id/sfc_order_price_content').waitFor();

// });




// //com.sdu.didi.psnger:id/bt_refresh
// // var num = id('com.sdu.didi.psnger:id/from_tv_tag').find().size();

// // log(num);



// toast('嘴  qq:1569978217');










